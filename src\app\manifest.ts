import type { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest[] {
    return [
        {
            name: 'CIP TopUp',
            short_name: 'CIP TopUp',
            description:
                'Fast & secure bill payments, data subscriptions, and airtime topup without signup. Instant confirmation and seamless activation.',
            start_url: '/',
            display: 'standalone',
            background_color: '#ffffff',
            theme_color: '#004eec',
            orientation: 'portrait-primary',
            lang: 'en',
            icons: [
                {
                    src: '/favicon.ico',
                    sizes: '48x48',
                    type: 'image/x-icon',
                },
                {
                    src: '/android-chrome-192x192.png',
                    sizes: '192x192',
                    type: 'image/png',
                },
                {
                    src: '/android-chrome-512x512.png',
                    sizes: '512x512',
                    type: 'image/png',
                },
                {
                    src: '/apple-touch-icon.png',
                    sizes: '180x180',
                    type: 'image/png',
                },
            ],
            screenshots: [
                {
                    src: '/img/screenshot-mobile.png',
                    sizes: '375x812',
                    type: 'image/png',
                    form_factor: 'narrow',
                    label: 'CIP TopUp Mobile Interface',
                },
                {
                    src: '/img/screenshot-desktop.png',
                    sizes: '1280x720',
                    type: 'image/png',
                    form_factor: 'wide',
                    label: 'CIP TopUp Desktop Interface',
                },
            ],
        },
    ]
}
